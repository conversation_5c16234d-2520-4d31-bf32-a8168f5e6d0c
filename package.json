{"name": "smileFactory", "version": "1.0.0", "private": true, "description": "The smileFactory platform is designed to bridge the gap between startups, innovators, and supporting organizations within Zimbabwe's growing innovation ecosystem.", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:playwright": "playwright test", "test:playwright:ui": "playwright test --ui", "test:playwright:headed": "playwright test --headed", "clean": "rm -rf node_modules package-lock.json && npm install", "clean:win": "rmdir /s /q node_modules && del package-lock.json && npm install"}, "dependencies": {"@chenfengyuan/vue-countdown": "^2.1.3", "@quasar/extras": "^1.16.17", "@supabase/supabase-js": "^2.49.4", "@types/dompurify": "^3.0.5", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "fuse.js": "^7.0.0", "openai": "^4.86.1", "pinia": "^2.1.7", "quasar": "^2.18.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@playwright/test": "^1.53.2", "@quasar/vite-plugin": "^1.9.0", "@types/node": "^20.11.16", "@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-v8": "^1.6.1", "@vitest/ui": "^1.6.1", "@vue/compiler-sfc": "^3.5.13", "@vue/test-utils": "^2.4.3", "happy-dom": "^13.3.8", "sass": "^1.86.0", "supabase": "^2.20.12", "typescript": "^5.7.2", "vite": "^6.2.0", "vitest": "^1.6.1"}, "type": "module", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["vue", "vite", "quasar", "innovation", "zimbabwe"], "author": "", "license": "ISC"}