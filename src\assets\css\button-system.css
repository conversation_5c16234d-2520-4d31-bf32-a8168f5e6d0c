/* smileFactory Button Design System */

/* Primary CTA Button - Main action buttons (like "Explore Community") */
.zb-btn-primary {
  background: linear-gradient(135deg, #0D8A3E 0%, #0B7235 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 25px !important;
  min-width: 160px !important;
  height: 40px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  text-transform: none !important;
  box-shadow: 0 4px 12px rgba(13, 138, 62, 0.3) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Ensure AI chat buttons and related components have consistent flat styling */
.ai-chat-container .q-btn,
.chat-window .q-btn,
.action-btn,
.ai-trigger-button,
.ai-triggers-section .q-btn,
.profile-ai-triggers-card .q-btn,
.smart-ai-triggers .q-btn,
.interaction-buttons .q-btn {
  background: unset !important;
  color: unset !important;
  border: unset !important;
  border-radius: 6px !important;
  min-width: unset !important;
  height: unset !important;
  font-weight: unset !important;
  font-size: unset !important;
  text-transform: none !important;
  box-shadow: none !important;
  transition: all 0.2s ease !important;
}

/* Flat button hover states for AI components */
.ai-chat-container .q-btn:hover,
.chat-window .q-btn:hover,
.action-btn:hover,
.ai-trigger-button:hover,
.ai-triggers-section .q-btn:hover,
.profile-ai-triggers-card .q-btn:hover,
.smart-ai-triggers .q-btn:hover,
.interaction-buttons .q-btn:hover {
  background-color: rgba(13, 138, 62, 0.08) !important;
}

.ai-chat-container .q-btn:active,
.chat-window .q-btn:active,
.action-btn:active,
.ai-trigger-button:active,
.ai-triggers-section .q-btn:active,
.profile-ai-triggers-card .q-btn:active,
.smart-ai-triggers .q-btn:active,
.interaction-buttons .q-btn:active {
  background-color: rgba(13, 138, 62, 0.12) !important;
}

.zb-btn-primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(13, 138, 62, 0.4) !important;
  background: linear-gradient(135deg, #0F9B45 0%, #0D8A3E 100%) !important;
}

.zb-btn-primary:active {
  transform: translateY(0) !important;
  box-shadow: 0 4px 12px rgba(13, 138, 62, 0.3) !important;
}

/* Secondary Outline Button - Secondary actions */
.zb-btn-secondary {
  background: transparent !important;
  color: #a4ca39 !important;
  border: 2px solid #a4ca39 !important;
  border-radius: 25px !important;
  min-width: 160px !important;
  height: 40px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  text-transform: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Scope button system to specific dashboard components only */
.dashboard-welcome-section .q-btn,
.profile-overview-card .q-btn,
.cta-button,
.primary-btn {
  /* These will inherit the button system styles when they have the appropriate classes */
}

.zb-btn-secondary:hover {
  background: #a4ca39 !important;
  color: white !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(164, 202, 57, 0.3) !important;
}

.zb-btn-secondary:active {
  transform: translateY(0) !important;
}

/* Flat Action Button - For cards and inline actions */
.zb-btn-flat {
  background: transparent !important;
  color: #0D8A3E !important;
  border: none !important;
  border-radius: 6px !important;
  min-width: 120px !important;
  height: 36px !important;
  font-weight: 500 !important;
  font-size: 13px !important;
  text-transform: none !important;
  transition: all 0.2s ease !important;
}

.zb-btn-flat:hover {
  background: rgba(13, 138, 62, 0.1) !important;
  transform: translateY(-1px) !important;
}

/* Small Button - For compact spaces */
.zb-btn-small {
  background: #0D8A3E !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  min-width: 80px !important;
  height: 32px !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  text-transform: none !important;
  transition: all 0.2s ease !important;
}

.zb-btn-small:hover {
  background: #0F9B45 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(13, 138, 62, 0.2) !important;
}

/* Icon Button - For icon-only actions */
.zb-btn-icon {
  background: rgba(13, 138, 62, 0.1) !important;
  color: #0D8A3E !important;
  border: none !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  transition: all 0.2s ease !important;
}

.zb-btn-icon:hover {
  background: #0D8A3E !important;
  color: white !important;
  transform: scale(1.1) !important;
}

/* Danger Button - For destructive actions */
.zb-btn-danger {
  background: #C10015 !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  min-width: 120px !important;
  height: 36px !important;
  font-weight: 500 !important;
  font-size: 13px !important;
  text-transform: none !important;
  transition: all 0.2s ease !important;
}

.zb-btn-danger:hover {
  background: #A00012 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(193, 0, 21, 0.3) !important;
}

/* Success Button - For positive actions */
.zb-btn-success {
  background: #21BA45 !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  min-width: 120px !important;
  height: 36px !important;
  font-weight: 500 !important;
  font-size: 13px !important;
  text-transform: none !important;
  transition: all 0.2s ease !important;
}

.zb-btn-success:hover {
  background: #1EA940 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(33, 186, 69, 0.3) !important;
}

/* Loading state for all buttons */
.zb-btn-primary.q-btn--loading,
.zb-btn-secondary.q-btn--loading,
.zb-btn-flat.q-btn--loading,
.zb-btn-small.q-btn--loading,
.zb-btn-danger.q-btn--loading,
.zb-btn-success.q-btn--loading {
  pointer-events: none !important;
  opacity: 0.7 !important;
}

/* Disabled state for all buttons */
.zb-btn-primary.q-btn--disabled,
.zb-btn-secondary.q-btn--disabled,
.zb-btn-flat.q-btn--disabled,
.zb-btn-small.q-btn--disabled,
.zb-btn-danger.q-btn--disabled,
.zb-btn-success.q-btn--disabled {
  opacity: 0.5 !important;
  pointer-events: none !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
  .zb-btn-primary,
  .zb-btn-secondary {
    width: calc(100% - 16px) !important;
    margin: 4px 8px !important;
    min-width: unset !important;
  }
  
  .zb-btn-flat {
    min-width: 100px !important;
  }
  
  .zb-btn-small {
    min-width: 70px !important;
  }
}

/* Animation classes */
.zb-btn-bounce {
  animation: zb-bounce 2s infinite;
}

@keyframes zb-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

.zb-btn-pulse {
  animation: zb-pulse 2s infinite;
}

@keyframes zb-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(13, 138, 62, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(13, 138, 62, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(13, 138, 62, 0);
  }
}

/* Focus states for accessibility */
.zb-btn-primary:focus,
.zb-btn-secondary:focus,
.zb-btn-flat:focus,
.zb-btn-small:focus,
.zb-btn-danger:focus,
.zb-btn-success:focus,
.zb-btn-icon:focus {
  outline: 2px solid #a4ca39 !important;
  outline-offset: 2px !important;
}
