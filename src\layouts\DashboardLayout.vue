<template>
  <q-layout view="hHh LpR lFf">
    <q-header elevated class="bg-menu text-primary">
      <q-toolbar>
        <q-btn flat round dense class="menu-btn" @click="toggleLeftDrawer">
          <unified-icon name="menu" />
        </q-btn>
        <q-toolbar-title>
          <div class="logo-container">
            <img src="/smile-factory-logo.svg" alt="Smile Factory Logo" class="logo-image">
          </div>
        </q-toolbar-title>

        <!-- Home button to navigate to landing page -->
        <q-btn
          flat
          dense
          color="primary"
          @click="navigateToHome"
          class="q-mr-sm toolbar-btn"
          round
        >
          <unified-icon name="home" size="20px" />
          <span class="gt-xs">Home</span>
        </q-btn>

        <!-- Messages button with notification badge -->
        <q-btn
          flat
          dense
          color="primary"
          to="/dashboard/messages"
          class="q-mr-sm toolbar-btn"
          round
        >
          <notification-badge
            :count="unreadMessageCount"
            color="red"
            floating
            :animate="unreadMessageCount > 0"
          />
          <unified-icon name="mail" size="20px" />
          <span class="gt-xs">Messages</span>
        </q-btn>

        <!-- Activity button with notification badge -->
        <q-btn
          flat
          dense
          color="primary"
          to="/dashboard/activity"
          class="q-mr-sm toolbar-btn"
          round
        >
          <notification-badge
            :count="totalActivityCount"
            color="orange"
            floating
            :animate="totalActivityCount > 0"
          />
          <unified-icon name="today" size="20px" />
          <span class="gt-xs">Activity</span>

          <!-- Activity dropdown menu -->
          <q-menu>
            <q-list style="min-width: 250px">
              <q-item class="q-pa-sm bg-grey-2">
                <q-item-section>
                  <div class="text-subtitle1 text-weight-medium">Notifications</div>
                </q-item-section>
              </q-item>

              <q-item clickable v-ripple to="/dashboard/connections">
                <q-item-section avatar>
                  <unified-icon name="person_add" />
                </q-item-section>
                <q-item-section>Connection Requests</q-item-section>
                <q-item-section side v-if="connectionRequestsCount > 0">
                  <notification-badge :count="connectionRequestsCount" color="red" rounded />
                </q-item-section>
              </q-item>

              <q-item clickable v-ripple to="/dashboard/activity">
                <q-item-section avatar>
                  <unified-icon name="access-time" />
                </q-item-section>
                <q-item-section>Recent Activity</q-item-section>
                <q-item-section side v-if="unreadActivitiesCount > 0">
                  <notification-badge :count="unreadActivitiesCount" color="blue" rounded />
                </q-item-section>
              </q-item>

              <q-separator />

              <q-item clickable v-ripple to="/dashboard/messages">
                <q-item-section avatar>
                  <unified-icon name="chat" />
                </q-item-section>
                <q-item-section>Messages</q-item-section>
                <q-item-section side v-if="unreadMessageCount > 0">
                  <notification-badge :count="unreadMessageCount" color="green" rounded />
                </q-item-section>
              </q-item>

              <q-separator />

              <q-item clickable v-ripple to="/dashboard/notifications" class="text-primary">
                <q-item-section class="text-center">
                  View All Notifications
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>

        <q-btn flat round dense class="account-btn toolbar-btn cursor-pointer">
          <unified-icon name="account_circle"  size="20px" />
          <span class="gt-xs">Account</span>
          <q-menu>
            <q-list style="min-width: 150px">
              <q-item clickable @click="navigateTo('/dashboard/profile')">
                <q-item-section>Profile</q-item-section>
              </q-item>
              <q-separator />
              <q-item clickable @click="handleLogout">
                <q-item-section>Logout</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
      :width="240"
      class="bg-white"
    >
      <q-scroll-area class="fit">
        <q-list padding>
          <q-item-label header>Navigation</q-item-label>

          <q-item clickable v-ripple to="/dashboard" exact active-class="q-router-link-active">
            <q-item-section avatar>
              <unified-icon name="dashboard" />
            </q-item-section>
            <q-item-section>Dashboard</q-item-section>
          </q-item>

          <q-item clickable v-ripple to="/dashboard/profile" active-class="q-router-link-active">
            <q-item-section avatar>
              <unified-icon name="person" />
            </q-item-section>
            <q-item-section>Profile</q-item-section>
          </q-item>

          <q-expansion-item
            expand-separator
            icon="article"
            label="My Content"
            :default-opened="currentPath.includes('/dashboard/content')"
          >
            <q-list>
              <q-item clickable v-ripple to="/dashboard/content?tab=posts" active-class="q-router-link-active">
                <q-item-section avatar>
                  <unified-icon name="post_add" />
                </q-item-section>
                <q-item-section>Posts</q-item-section>
              </q-item>

              <q-item clickable v-ripple to="/dashboard/content?tab=groups" active-class="q-router-link-active">
                <q-item-section avatar>
                  <unified-icon name="groups" />
                </q-item-section>
                <q-item-section>Groups</q-item-section>
              </q-item>

              <q-item clickable v-ripple to="/dashboard/content?tab=marketplace" active-class="q-router-link-active">
                <q-item-section avatar>
                  <unified-icon name="store" />
                </q-item-section>
                <q-item-section>Marketplace</q-item-section>
              </q-item>

              <q-item clickable v-ripple to="/dashboard/content?tab=comments" active-class="q-router-link-active">
                <q-item-section avatar>
                  <unified-icon name="comment" />
                </q-item-section>
                <q-item-section>Comments</q-item-section>
              </q-item>
            </q-list>
          </q-expansion-item>

          <!-- Mentorship Hub Navigation -->
          <q-item
            v-if="shouldShowMentorshipNav"
            clickable
            v-ripple
            :to="mentorshipRoute"
            active-class="q-router-link-active"
          >
            <q-item-section avatar>
              <unified-icon name="school" />
            </q-item-section>
            <q-item-section>{{ mentorshipLabel }}</q-item-section>
            <q-item-section side>
              <q-badge :color="mentorshipBadgeColor">{{ mentorshipBadgeText }}</q-badge>
            </q-item-section>
          </q-item>

          <q-expansion-item
            expand-separator
            icon="notifications"
            label="My Activity"
            :default-opened="currentPath.includes('/dashboard/activity') ||
                            currentPath.includes('/dashboard/connections') ||
                            currentPath.includes('/dashboard/messages')"
          >
            <!-- Add badge to the expansion item itself if there are any notifications -->
            <template v-if="totalActivityCount > 0" v-slot:header-right>
              <notification-badge
                :count="totalActivityCount"
                color="orange"
                rounded
                :animate="totalActivityCount > 0"
              />
            </template>

            <q-list>
              <q-item clickable v-ripple to="/dashboard/activity" active-class="q-router-link-active">
                <q-item-section avatar>
                  <unified-icon name="history" />
                </q-item-section>
                <q-item-section>All Activity</q-item-section>
                <q-item-section side v-if="unreadActivitiesCount > 0">
                  <notification-badge :count="unreadActivitiesCount" color="blue" rounded />
                </q-item-section>
              </q-item>

              <q-item clickable v-ripple to="/dashboard/notifications" active-class="q-router-link-active">
                <q-item-section avatar>
                  <unified-icon name="notifications" />
                </q-item-section>
                <q-item-section>Notifications</q-item-section>
                <q-item-section side v-if="userNotificationsCount > 0">
                  <notification-badge :count="userNotificationsCount" color="purple" rounded />
                </q-item-section>
              </q-item>

              <q-item clickable v-ripple to="/dashboard/messages" active-class="q-router-link-active">
                <q-item-section avatar>
                  <unified-icon name="chat" />
                </q-item-section>
                <q-item-section>Messages</q-item-section>
                <q-item-section side v-if="unreadMessageCount > 0">
                  <notification-badge :count="unreadMessageCount" color="green" rounded />
                </q-item-section>
              </q-item>

              <q-item clickable v-ripple to="/dashboard/connections" active-class="q-router-link-active">
                <q-item-section avatar>
                  <unified-icon name="people" />
                </q-item-section>
                <q-item-section>
                  Connections
                  <q-item-label caption v-if="connectionsCount > 0">
                    {{ connectionsCount }} connection{{ connectionsCount > 1 ? 's' : '' }}
                  </q-item-label>
                </q-item-section>
                <q-item-section side v-if="connectionRequestsCount > 0">
                  <notification-badge :count="connectionRequestsCount" color="red" rounded />
                </q-item-section>
              </q-item>
            </q-list>
          </q-expansion-item>

          <!-- Duplicate marketplace link removed -->

          <q-separator spaced />
          <q-item-label header>Coming Soon</q-item-label>

          <!-- MATCHMAKING NAVIGATION - TEMPORARILY DISABLED -->
          <!-- Uncomment this item to re-enable matchmaking navigation -->
          <!-- <q-item clickable v-ripple to="/dashboard/matchmaking" active-class="q-router-link-active">
            <q-item-section avatar>
              <unified-icon name="people_alt" />
            </q-item-section>
            <q-item-section>Matchmaking</q-item-section>
            <q-item-section side>
              <q-badge color="green">New</q-badge>
            </q-item-section>
          </q-item> -->

          <!-- Mentorship Hub Navigation -->
          <q-item
            v-if="shouldShowMentorshipNav"
            clickable
            v-ripple
            :to="mentorshipRoute"
            active-class="q-router-link-active"
          >
            <q-item-section avatar>
              <unified-icon name="school" />
            </q-item-section>
            <q-item-section>{{ mentorshipLabel }}</q-item-section>
            <q-item-section side>
              <q-badge :color="mentorshipBadgeColor">{{ mentorshipBadgeText }}</q-badge>
            </q-item-section>
          </q-item>

          <q-item clickable v-ripple disable>
            <q-item-section avatar>
              <unified-icon name="attach_money" />
            </q-item-section>
            <q-item-section>Funding</q-item-section>
            <q-item-section side>
              <q-badge color="orange">Soon</q-badge>
            </q-item-section>
          </q-item>

          <q-item clickable v-ripple disable>
            <q-item-section avatar>
              <unified-icon name="upcoming" />
            </q-item-section>
            <q-item-section>Upcoming Activity</q-item-section>
            <q-item-section side>
              <q-badge color="orange">Soon</q-badge>
            </q-item-section>
          </q-item>

          <!-- Projects menu item removed -->

          <q-separator spaced />

          <q-item-label header>Support</q-item-label>

          <q-item clickable v-ripple to="/dashboard/help" active-class="q-router-link-active">
            <q-item-section avatar>
              <unified-icon name="help" />
            </q-item-section>
            <q-item-section>Help Center</q-item-section>
          </q-item>

          <q-item clickable v-ripple to="/dashboard/feedback" active-class="q-router-link-active">
            <q-item-section avatar>
              <unified-icon name="feedback" />
            </q-item-section>
            <q-item-section>Feedback</q-item-section>
          </q-item>

          <q-separator spaced />

          <!-- AI Assistant Widget removed - using main chat component -->

        </q-list>
      </q-scroll-area>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>

    <!-- AI Chat Assistant -->
    <AIChatAssistant />
  </q-layout>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useAuthStore } from '../stores/auth'
import { useProfileStore } from '../stores/profile'
import { useLayoutStore } from '../stores/layout'
import { useMessagingStore } from '../stores/messaging'
import { useActivityNotificationsStore } from '../stores/activityNotifications'
import { useUserNotificationsStore } from '../stores/userNotifications'
import { useConnectionsStore } from '../stores/connections'
import UnifiedIcon from '../components/ui/UnifiedIcon.vue'
import NotificationBadge from '../components/common/NotificationBadge.vue'
import AIChatAssistant from '../components/ai/AIChatAssistant.vue'
import { serviceCoordinator, ServiceNames } from '../services/ServiceCoordinator'

const router = useRouter()
const $q = useQuasar()
const authStore = useAuthStore()
const profileStore = useProfileStore()
const layoutStore = useLayoutStore()
const messagingStore = useMessagingStore()
const activityNotificationsStore = useActivityNotificationsStore()
const userNotificationsStore = useUserNotificationsStore()
const connectionsStore = useConnectionsStore()

// Get current path safely for tests
const currentPath = computed(() => router.currentRoute.value?.path || '')

// Get unread message count
const unreadMessageCount = computed(() => messagingStore.unreadCount)

// Get activity notification counts
const connectionRequestsCount = computed(() => activityNotificationsStore.connectionRequests)
const unreadActivitiesCount = computed(() => activityNotificationsStore.unreadActivities)
const totalActivityCount = computed(() => activityNotificationsStore.totalUnreadCount)

// Get user notifications count
const userNotificationsCount = computed(() => userNotificationsStore.unreadCount)

// Get connections count
const connectionsCount = computed(() => connectionsStore.connectionsCount)

// Mentorship Hub Navigation Logic
const currentProfile = computed(() => profileStore.currentProfile)
const profileType = computed(() => currentProfile.value?.profile_type)

// Determine if mentorship navigation should be shown
const shouldShowMentorshipNav = computed(() => {
  const type = profileType.value
  // Show for profiles that can receive mentorship (innovators, students) or give mentorship (mentors)
  return type === 'innovator' || type === 'academic_student' || type === 'mentor'
})

// Mentorship route - always goes to mentorship hub
const mentorshipRoute = computed(() => {
  return '/dashboard/mentorship' // All users go to mentorship dashboard
})

// Mentorship label - always shows Mentorship Hub
const mentorshipLabel = computed(() => {
  return 'Mentorship Hub'
})

// Dynamic badge color and text
const mentorshipBadgeColor = computed(() => {
  const type = profileType.value
  if (type === 'mentor') {
    return 'purple'
  } else {
    return 'green'
  }
})

const mentorshipBadgeText = computed(() => {
  const type = profileType.value
  if (type === 'mentor') {
    return 'Hub'
  } else {
    return 'Find'
  }
})

// Watch for route changes to mark activities as read when visiting the activity page
watch(() => router.currentRoute.value.path, (newPath) => {
  if (newPath === '/dashboard/activity') {
    activityNotificationsStore.markActivitiesAsRead()
  }
  if (newPath === '/dashboard/connections') {
    // Reset connection requests count when visiting the connections page
    activityNotificationsStore.fetchConnectionRequestsCount()
    // Also refresh connections count
    connectionsStore.fetchUserConnections()
  }
  if (newPath === '/dashboard/notifications') {
    // Mark all notifications as read when visiting the notifications page
    userNotificationsStore.markAllAsRead()
    activityNotificationsStore.markActivitiesAsRead()
    activityNotificationsStore.markConnectionRequestsAsViewed()
  }
})

// Initialize services using ServiceCoordinator to prevent duplicates
onMounted(async () => {
  console.log('DashboardLayout: Starting coordinated service initialization')

  try {
    // Register and initialize services through ServiceCoordinator
    await Promise.all([
      serviceCoordinator.initializeService(ServiceNames.MESSAGING, async () => {
        await messagingStore.initializeMessaging()
      }),
      serviceCoordinator.initializeService(ServiceNames.ACTIVITY_NOTIFICATIONS, async () => {
        await activityNotificationsStore.initialize()
      }),
      serviceCoordinator.initializeService(ServiceNames.USER_NOTIFICATIONS, async () => {
        await userNotificationsStore.initialize()
      })
    ])

    // Initialize connections store to get connection count
    if (authStore.isAuthenticated) {
      await connectionsStore.fetchUserConnections()
    }

    console.log('✅ DashboardLayout: All services initialized successfully')
    console.log('Service stats:', serviceCoordinator.getStats())
  } catch (error) {
    console.error('❌ DashboardLayout: Service initialization error:', error)
  }

  // Set up periodic refresh of notification counts (every 10 minutes - reduced frequency)
  const refreshInterval = setInterval(() => {
    if (authStore.isAuthenticated) {
      activityNotificationsStore.fetchConnectionRequestsCount()
      activityNotificationsStore.fetchUnreadActivitiesCount()
      userNotificationsStore.fetchUnreadCount()
      // connectionsStore.fetchUserConnections() // REMOVED - Unnecessary frequent polling
    }
  }, 600000) // 10 minutes - Much less frequent

  // Clear interval on unmount
  onUnmounted(() => {
    clearInterval(refreshInterval)
  })
})

// Clean up messaging when component is unmounted
onUnmounted(() => {
  messagingStore.cleanupMessaging()
})

const leftDrawerOpen = ref(false)

const toggleLeftDrawer = () => {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

const navigateTo = (path: string) => {
  router.push(path)

  // If the path contains a hash, scroll to the element after navigation
  if (path.includes('#')) {
    const hash = path.split('#')[1]
    setTimeout(() => {
      const element = document.getElementById(hash)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }, 100)
  }
}

// Navigate to home page
const navigateToHome = async () => {
  await router.push('/')
}

const handleLogout = async () => {
  try {
    // authStore.signOut() already shows a notification and redirects
    await authStore.signOut()
    // No need for additional notification or redirect
  } catch (error: any) {
    // Only show error notification if the auth store didn't already show one
    $q.notify({
      type: 'negative',
      message: error.message || 'Failed to logout',
      position: 'top'
    })
  }
}
</script>

<style scoped>
.bg-menu {
  background-color: #dfefe6;
}

.q-drawer {
  background-color: #f5f5f5;
}

.q-toolbar {
  color: #0D8A3E;
}

.cursor-pointer {
  cursor: pointer;
}

.q-item.q-router-link-active {
  color: #0D8A3E;
  background: rgba(13, 138, 62, 0.1);
  font-weight: 600;
  border-left: 3px solid #0D8A3E;
}

.q-item {
  color: #424242;
}

.q-item:hover {
  color: #0D8A3E;
  background: rgba(13, 138, 62, 0.05);
}

.q-item-label {
  color: #666666;
  font-weight: 500;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-image {
  height: 32px;
  width: auto;
}

.logo-text {
  font-weight: bold;
  font-size: 1.2rem;
}

.icon {
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.menu-btn .icon,
.account-btn .icon {
  width: 24px;
  height: 24px;
}

/* Toolbar button styles */
.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #dfefe6;
}

/* Responsive adjustments for mobile */
@media (max-width: 599px) {
  .q-toolbar .q-btn {
    min-width: unset;
    padding: 4px;
  }

  /* Ensure icons are properly sized on mobile */
  .q-toolbar .q-btn unified-icon {
    font-size: 1.2rem;
  }

  /* Add some spacing between toolbar buttons on mobile */
  .q-toolbar .q-btn.q-mr-sm {
    margin-right: 4px !important;
  }

  /* Ensure toolbar buttons are properly aligned on mobile */
  .toolbar-btn {
    width: 36px;
    height: 36px;
  }
}

.q-item-section.avatar .icon {
  width: 24px;
  height: 24px;
}
</style>
